package com.example.chat;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.EqualsAndHashCode;

/**
 * 测试Lombok功能
 */
public class LombokTest {
    
    public static void main(String[] args) {
        System.out.println("=== Lombok功能测试 ===");
        
        // 测试@Data注解
        testDataAnnotation();
        
        // 测试@Builder注解
        testBuilderAnnotation();
        
        // 测试单独的注解
        testIndividualAnnotations();
        
        System.out.println("=== 所有测试通过！Lombok配置成功 ===");
    }
    
    private static void testDataAnnotation() {
        System.out.println("\n1. 测试@Data注解:");
        
        Person person = new Person();
        person.setName("张三");
        person.setAge(25);
        person.setEmail("<EMAIL>");
        
        System.out.println("Person: " + person);
        System.out.println("Name: " + person.getName());
        System.out.println("Age: " + person.getAge());
        
        // 测试equals和hashCode
        Person person2 = new Person();
        person2.setName("张三");
        person2.setAge(25);
        person2.setEmail("<EMAIL>");
        
        System.out.println("两个Person对象相等: " + person.equals(person2));
        System.out.println("HashCode相同: " + (person.hashCode() == person2.hashCode()));
    }
    
    private static void testBuilderAnnotation() {
        System.out.println("\n2. 测试@Builder注解:");
        
        Product product = Product.builder()
            .id(1L)
            .name("笔记本电脑")
            .price(5999.99)
            .category("电子产品")
            .build();
        
        System.out.println("Product: " + product);
    }
    
    private static void testIndividualAnnotations() {
        System.out.println("\n3. 测试单独注解:");
        
        User user = new User("李四", "<EMAIL>");
        System.out.println("User: " + user);
        
        user.setName("李四修改");
        System.out.println("修改后的Name: " + user.getName());
    }
    
    /**
     * 使用@Data注解的类
     * 自动生成getter、setter、toString、equals、hashCode方法
     */
    @Data
    public static class Person {
        private String name;
        private int age;
        private String email;
    }
    
    /**
     * 使用@Builder注解的类
     * 自动生成建造者模式的代码
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Product {
        private Long id;
        private String name;
        private Double price;
        private String category;
    }
    
    /**
     * 使用单独注解的类
     */
    @Getter
    @Setter
    @ToString
    @EqualsAndHashCode
    @AllArgsConstructor
    @NoArgsConstructor
    public static class User {
        private String name;
        private String email;
        
        public User(String name, String email) {
            this.name = name;
            this.email = email;
        }
    }
}
