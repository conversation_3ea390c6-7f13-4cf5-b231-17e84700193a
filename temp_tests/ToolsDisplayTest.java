package com.example.chat;

import com.example.chat.service.Tool;
import com.example.chat.tools.DirectoryListTool;
import com.example.chat.tools.SystemInfoTool;
import javax.swing.SwingUtilities;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 测试工具展示功能
 */
public class ToolsDisplayTest {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // 创建聊天助手实例
            CopilotChatAssistant chatAssistant = new CopilotChatAssistant();
            
            // 创建测试工具列表
            List<Tool> tools = createTestTools();
            
            // 设置工具列表
            chatAssistant.setAvailableTools(tools);
            
            // 注册基本事件监听器
            chatAssistant.onMessageSent(userMessage -> {
                System.out.println("用户发送消息: " + userMessage);
            });
            
            // 显示窗口
            chatAssistant.setVisible(true);
            
            System.out.println("=== 工具展示功能测试 ===");
            System.out.println("1. 点击标题栏的工具按钮（🔧）来显示/隐藏工具面板");
            System.out.println("2. 在工具面板中可以看到所有可用工具");
            System.out.println("3. 点击工具卡片查看详细信息");
            System.out.println("4. 工具面板显示工具名称、描述和参数数量");
            System.out.println("========================");
        });
    }
    
    /**
     * 创建测试工具列表
     */
    private static List<Tool> createTestTools() {
        List<Tool> tools = new ArrayList<>();
        
        // 添加现有工具
        tools.add(DirectoryListTool.createTool());
        tools.add(SystemInfoTool.createTool());
        
        // 添加自定义测试工具
        tools.add(createCustomTool1());
        tools.add(createCustomTool2());
        tools.add(createCustomTool3());
        
        return tools;
    }
    
    /**
     * 创建自定义测试工具1 - 文本处理工具
     */
    private static Tool createCustomTool1() {
        List<Tool.ToolParameter> parameters = Arrays.asList(
            new Tool.ToolParameter("text", "string", "要处理的文本内容", true),
            new Tool.ToolParameter("operation", "string", "处理操作类型", true, null, 
                Arrays.asList("uppercase", "lowercase", "reverse", "count_words")),
            new Tool.ToolParameter("trim_whitespace", "boolean", "是否去除首尾空白字符", false, true)
        );
        
        return new Tool("text_processor", "文本处理工具，支持大小写转换、反转、单词计数等操作", parameters, 
            (args) -> {
                String text = (String) args.get("text");
                String operation = (String) args.get("operation");
                Boolean trim = (Boolean) args.getOrDefault("trim_whitespace", true);
                
                if (trim) {
                    text = text.trim();
                }
                
                String result;
                switch (operation) {
                    case "uppercase":
                        result = text.toUpperCase();
                        break;
                    case "lowercase":
                        result = text.toLowerCase();
                        break;
                    case "reverse":
                        result = new StringBuilder(text).reverse().toString();
                        break;
                    case "count_words":
                        result = "单词数量: " + text.split("\\s+").length;
                        break;
                    default:
                        result = "不支持的操作: " + operation;
                }
                
                return new com.example.chat.service.ToolExecutionResult(result);
            });
    }
    
    /**
     * 创建自定义测试工具2 - 数学计算工具
     */
    private static Tool createCustomTool2() {
        List<Tool.ToolParameter> parameters = Arrays.asList(
            new Tool.ToolParameter("expression", "string", "数学表达式，如 '2 + 3 * 4'", true),
            new Tool.ToolParameter("precision", "number", "结果精度（小数位数）", false, 2)
        );
        
        return new Tool("math_calculator", "简单的数学计算器，支持基本的四则运算", parameters,
            (args) -> {
                String expression = (String) args.get("expression");
                Number precision = (Number) args.getOrDefault("precision", 2);
                
                try {
                    // 这里只是一个简单的示例，实际应用中需要更安全的表达式解析
                    String result = "计算结果: " + expression + " = [计算功能未实现]";
                    return new com.example.chat.service.ToolExecutionResult(result);
                } catch (Exception e) {
                    return com.example.chat.service.ToolExecutionResult.failure("CALCULATION_ERROR", 
                        "计算失败: " + e.getMessage());
                }
            });
    }
    
    /**
     * 创建自定义测试工具3 - 时间工具
     */
    private static Tool createCustomTool3() {
        List<Tool.ToolParameter> parameters = Arrays.asList(
            new Tool.ToolParameter("format", "string", "时间格式", false, "yyyy-MM-dd HH:mm:ss"),
            new Tool.ToolParameter("timezone", "string", "时区", false, "Asia/Shanghai")
        );
        
        return new Tool("current_time", "获取当前时间，支持自定义格式和时区", parameters,
            (args) -> {
                String format = (String) args.getOrDefault("format", "yyyy-MM-dd HH:mm:ss");
                String timezone = (String) args.getOrDefault("timezone", "Asia/Shanghai");
                
                try {
                    java.time.LocalDateTime now = java.time.LocalDateTime.now();
                    java.time.format.DateTimeFormatter formatter = 
                        java.time.format.DateTimeFormatter.ofPattern(format);
                    String result = "当前时间 (" + timezone + "): " + now.format(formatter);
                    return new com.example.chat.service.ToolExecutionResult(result);
                } catch (Exception e) {
                    return com.example.chat.service.ToolExecutionResult.failure("TIME_ERROR", 
                        "获取时间失败: " + e.getMessage());
                }
            });
    }
}
