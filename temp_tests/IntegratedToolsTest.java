package com.example.chat;

import com.example.chat.service.DefaultFunctionCallManager;
import com.example.chat.service.FunctionCallManager;
import com.example.chat.service.Tool;
import com.example.chat.tools.DirectoryListTool;
import com.example.chat.tools.SystemInfoTool;
import com.example.chat.model.OpenAIRequest;
import javax.swing.SwingUtilities;
import java.util.ArrayList;
import java.util.List;

/**
 * 集成工具展示功能的完整测试
 * 演示如何在实际应用中使用工具展示功能
 */
public class IntegratedToolsTest {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // 创建聊天助手实例
            CopilotChatAssistant chatAssistant = new CopilotChatAssistant();
            
            // 创建功能管理器并注册工具
            FunctionCallManager functionManager = new DefaultFunctionCallManager();
            functionManager.registerTool(DirectoryListTool.createTool());
            functionManager.registerTool(SystemInfoTool.createTool());
            
            // 获取已注册的工具列表并设置到聊天助手
            List<Tool> registeredTools = functionManager.getRegisteredTools();
            chatAssistant.setAvailableTools(registeredTools);
            
            // 注册消息发送监听器
            chatAssistant.onMessageSent(userMessage -> {
                System.out.println("=== 用户发送消息 ===");
                System.out.println("消息内容: " + userMessage);
                handleUserMessage(chatAssistant, userMessage, functionManager);
            });
            
            // 注册其他事件监听器
            chatAssistant.onMessageEdited((messageIndex, newContent) -> {
                System.out.println("=== 消息编辑事件 ===");
                System.out.println("消息索引: " + messageIndex + ", 新内容: " + newContent);
            });
            
            // 设置初始消息
            setupInitialMessages(chatAssistant);
            
            // 显示窗口
            chatAssistant.setVisible(true);
            
            System.out.println("=== 集成工具展示功能测试 ===");
            System.out.println("功能说明：");
            System.out.println("1. 点击工具按钮（🔧）查看可用工具");
            System.out.println("2. 工具面板显示所有已注册的工具");
            System.out.println("3. 点击工具卡片查看详细参数信息");
            System.out.println("4. 发送消息测试正常聊天功能");
            System.out.println("5. 工具和聊天功能完全集成");
            System.out.println("===============================");
        });
    }
    
    /**
     * 处理用户消息
     */
    private static void handleUserMessage(ChatInterface chatInterface, String userMessage, 
                                        FunctionCallManager functionManager) {
        // 模拟AI处理消息
        new Thread(() -> {
            try {
                // 设置回复状态
                chatInterface.setReplyingStatus(true);
                
                // 模拟处理延迟
                Thread.sleep(1000);
                
                // 获取当前消息列表
                List<OpenAIRequest.Message> messages = getCurrentMessages(chatInterface);
                
                // 添加用户消息
                messages.add(new OpenAIRequest.Message("user", userMessage));
                
                // 生成AI回复
                String aiResponse = generateAIResponse(userMessage, functionManager);
                messages.add(new OpenAIRequest.Message("assistant", aiResponse));
                
                // 更新界面
                SwingUtilities.invokeLater(() -> {
                    chatInterface.setMessages(messages);
                    chatInterface.setReplyingStatus(false);
                });
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }
    
    /**
     * 生成AI回复
     */
    private static String generateAIResponse(String userMessage, FunctionCallManager functionManager) {
        String lowerMessage = userMessage.toLowerCase();
        
        // 检查是否询问工具相关问题
        if (lowerMessage.contains("工具") || lowerMessage.contains("tool") || lowerMessage.contains("功能")) {
            List<Tool> tools = functionManager.getRegisteredTools();
            StringBuilder response = new StringBuilder();
            response.append("我当前可以使用以下工具：\n\n");
            
            for (Tool tool : tools) {
                response.append("🔧 **").append(tool.getName()).append("**\n");
                response.append("   ").append(tool.getDescription()).append("\n");
                response.append("   参数数量: ").append(tool.getParameters().size()).append("\n\n");
            }
            
            response.append("你可以点击界面上的工具按钮（🔧）查看详细信息，或者直接告诉我你想要做什么！");
            return response.toString();
        }
        
        // 检查是否要求列出目录
        if (lowerMessage.contains("列出") || lowerMessage.contains("目录") || lowerMessage.contains("文件")) {
            return "我可以帮你列出目录内容！请点击工具面板中的 'list_directory' 工具查看详细参数，" +
                   "或者直接告诉我你想查看哪个目录的内容。";
        }
        
        // 检查是否询问系统信息
        if (lowerMessage.contains("系统") || lowerMessage.contains("内存") || lowerMessage.contains("信息")) {
            return "我可以获取系统信息！请查看工具面板中的 'get_system_info' 工具，" +
                   "它可以提供操作系统、内存使用情况等信息。";
        }
        
        // 默认回复
        return "你好！我是GitHub Copilot。我现在配备了多种工具来帮助你：\n\n" +
               "• 目录列表工具 - 查看文件和目录\n" +
               "• 系统信息工具 - 获取系统状态\n\n" +
               "点击界面上的工具按钮（🔧）可以查看所有可用工具的详细信息。\n\n" +
               "你想要我帮你做什么呢？";
    }
    
    /**
     * 获取当前消息列表
     */
    private static List<OpenAIRequest.Message> getCurrentMessages(ChatInterface chatInterface) {
        // 简化实现，返回基础消息列表
        List<OpenAIRequest.Message> messages = new ArrayList<>();
        
        messages.add(new OpenAIRequest.Message("system",
            "You are GitHub Copilot with access to various tools. Help users understand and use available tools."));
        
        return messages;
    }
    
    /**
     * 设置初始消息
     */
    private static void setupInitialMessages(ChatInterface chatInterface) {
        List<OpenAIRequest.Message> messages = new ArrayList<>();
        
        // 系统消息
        messages.add(new OpenAIRequest.Message("system",
            "You are GitHub Copilot with access to various tools."));

        // 欢迎消息
        messages.add(new OpenAIRequest.Message("assistant",
            "你好！我是GitHub Copilot，现在配备了强大的工具系统！🔧\n\n" +
            "**新功能亮点：**\n" +
            "• 点击标题栏的工具按钮（🔧）查看所有可用工具\n" +
            "• 工具面板显示每个工具的详细信息\n" +
            "• 点击工具卡片查看参数说明\n\n" +
            "**当前可用工具：**\n" +
            "• 目录列表工具 - 浏览文件系统\n" +
            "• 系统信息工具 - 获取系统状态\n\n" +
            "试试问我关于工具的问题，或者直接点击工具按钮探索吧！"));

        chatInterface.setMessages(messages);
    }
}
