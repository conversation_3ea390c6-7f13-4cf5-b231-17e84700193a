package com.example.chat;

import com.example.chat.model.OpenAIRequest;
import javax.swing.SwingUtilities;
import javax.swing.Timer;
import java.util.ArrayList;
import java.util.List;

/**
 * 测试"保存并重试"功能
 */
public class SaveAndRetryTest {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // 创建聊天助手实例
            CopilotChatAssistant chatAssistant = new CopilotChatAssistant();
            
            // 注册消息发送监听器
            chatAssistant.onMessageSent(userMessage -> {
                System.out.println("=== 消息发送事件 ===");
                System.out.println("用户发送了消息: " + userMessage);
                System.out.println("==================");
                handleUserMessage(chatAssistant, userMessage);
            });
            
            // 注册消息编辑监听器
            chatAssistant.onMessageEdited((messageIndex, newContent) -> {
                System.out.println("=== 消息编辑事件 ===");
                System.out.println("用户编辑了消息 [索引: " + messageIndex + "]: " + newContent);
                System.out.println("==================");
                handleMessageEdit(chatAssistant, messageIndex, newContent);
            });
            
            // 注册删除消息监听器
            chatAssistant.onDeleteMessages(messageIndices -> {
                System.out.println("=== 消息删除事件 ===");
                System.out.println("用户删除了消息，索引: " + messageIndices);
                System.out.println("==================");
            });
            
            // 设置初始消息
            setupInitialMessages(chatAssistant);
            
            // 显示窗口
            chatAssistant.setVisible(true);
            
            System.out.println("=== 保存并重试功能测试 ===");
            System.out.println("1. 点击任意消息的编辑按钮（✏️）");
            System.out.println("2. 修改消息内容");
            System.out.println("3. 点击'保存并重试'按钮");
            System.out.println("4. 观察控制台输出，应该先看到编辑事件，再看到发送事件");
            System.out.println("========================");
        });
    }
    
    /**
     * 设置初始测试消息
     */
    private static void setupInitialMessages(ChatInterface chatInterface) {
        List<OpenAIRequest.Message> messages = new ArrayList<>();
        
        // 系统消息
        messages.add(new OpenAIRequest.Message("system",
            "You are GitHub Copilot, a helpful AI assistant for developers."));

        // 欢迎消息
        messages.add(new OpenAIRequest.Message("assistant",
            "你好！我是GitHub Copilot。\n\n" +
            "测试说明：\n" +
            "• 点击消息旁边的 ✏️ 按钮来编辑消息\n" +
            "• 在编辑对话框中，你会看到三个按钮：\n" +
            "  - 取消：关闭对话框，不保存更改\n" +
            "  - 确认：保存更改并关闭对话框\n" +
            "  - 保存并重试：保存更改并重新发送消息\n\n" +
            "试试编辑这条消息并使用'保存并重试'功能！"));

        // 示例用户消息
        messages.add(new OpenAIRequest.Message("user",
            "这是一条测试消息，你可以编辑它并测试'保存并重试'功能。"));

        // 示例AI回复
        messages.add(new OpenAIRequest.Message("assistant",
            "我收到了你的测试消息。你可以：\n" +
            "1. 编辑上面的用户消息\n" +
            "2. 点击'保存并重试'按钮\n" +
            "3. 观察编辑和发送事件的触发顺序"));

        chatInterface.setMessages(messages);
    }
    
    /**
     * 处理用户发送的消息
     */
    private static void handleUserMessage(ChatInterface chatInterface, String userMessage) {
        System.out.println("处理消息发送:");
        System.out.println("  消息内容: " + userMessage);
        
        // 模拟AI回复
        Timer timer = new Timer(1000, e -> {
            List<OpenAIRequest.Message> currentMessages = getCurrentMessages(chatInterface);
            
            // 添加用户消息
            currentMessages.add(new OpenAIRequest.Message("user", userMessage));
            
            // 添加AI回复
            currentMessages.add(new OpenAIRequest.Message("assistant",
                "✅ 收到重试的消息！\n\n" +
                "消息内容: \"" + userMessage + "\"\n\n" +
                "这证明'保存并重试'功能正常工作：\n" +
                "1. ✅ 编辑事件已触发（消息已保存）\n" +
                "2. ✅ 发送事件已触发（消息已重新发送）\n\n" +
                "你可以继续测试其他消息的编辑功能。"));

            chatInterface.setMessages(currentMessages);
        });
        timer.setRepeats(false);
        timer.start();
    }
    
    /**
     * 处理消息编辑
     */
    private static void handleMessageEdit(ChatInterface chatInterface, int messageIndex, String newContent) {
        System.out.println("处理消息编辑:");
        System.out.println("  消息索引: " + messageIndex);
        System.out.println("  新内容: " + newContent);
        
        // 获取当前消息列表并更新指定消息
        List<OpenAIRequest.Message> currentMessages = getCurrentMessages(chatInterface);
        if (messageIndex < currentMessages.size()) {
            currentMessages.get(messageIndex).setContent(newContent);
            chatInterface.setMessages(currentMessages);
        }
    }
    
    /**
     * 获取当前消息列表（简化实现）
     */
    private static List<OpenAIRequest.Message> getCurrentMessages(ChatInterface chatInterface) {
        // 在实际应用中，你需要维护一个消息列表的状态
        // 这里为了简化测试，返回一个基础的消息列表
        List<OpenAIRequest.Message> messages = new ArrayList<>();
        
        messages.add(new OpenAIRequest.Message("system",
            "You are GitHub Copilot, a helpful AI assistant for developers."));

        messages.add(new OpenAIRequest.Message("assistant",
            "你好！我是GitHub Copilot。\n\n" +
            "测试说明：\n" +
            "• 点击消息旁边的 ✏️ 按钮来编辑消息\n" +
            "• 在编辑对话框中，你会看到三个按钮：\n" +
            "  - 取消：关闭对话框，不保存更改\n" +
            "  - 确认：保存更改并关闭对话框\n" +
            "  - 保存并重试：保存更改并重新发送消息\n\n" +
            "试试编辑这条消息并使用'保存并重试'功能！"));

        return messages;
    }
}
