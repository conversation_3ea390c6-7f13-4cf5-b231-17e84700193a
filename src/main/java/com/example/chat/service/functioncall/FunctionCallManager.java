package com.example.chat.service.functioncall;

import java.util.List;
import java.util.Map;

/**
 * Function Call 管理器接口
 * 负责工具的注册、管理和调用
 */
public interface FunctionCallManager {
    
    /**
     * 注册工具
     * @param tool 要注册的工具
     */
    void registerTool(Tool tool);
    
    /**
     * 批量注册工具
     * @param tools 要注册的工具列表
     */
    void registerTools(List<Tool> tools);
    
    /**
     * 获取所有已注册的工具列表
     * @return 工具列表
     */
    List<Tool> getRegisteredTools();
    
    /**
     * 根据名称获取工具详情
     * @param toolName 工具名称
     * @return 工具对象，如果不存在则返回null
     */
    Tool getTool(String toolName);
    
    /**
     * 检查工具是否已注册
     * @param toolName 工具名称
     * @return 如果已注册返回true，否则返回false
     */
    boolean isToolRegistered(String toolName);
    
    /**
     * 调用指定工具并获取执行结果
     * @param toolName 工具名称
     * @param arguments 工具参数（JSON字符串格式）
     * @return 工具执行结果
     * @throws ToolExecutionException 工具执行异常
     */
    ToolExecutionResult callTool(String toolName, String arguments) throws ToolExecutionException;
    
    /**
     * 调用指定工具并获取执行结果
     * @param toolName 工具名称
     * @param arguments 工具参数（Map格式）
     * @return 工具执行结果
     * @throws ToolExecutionException 工具执行异常
     */
    ToolExecutionResult callTool(String toolName, Map<String, Object> arguments) throws ToolExecutionException;
    
    /**
     * 获取工具的OpenAI Function定义（用于API调用）
     * @return OpenAI Function定义列表
     */
    List<Map<String, Object>> getOpenAIFunctionDefinitions();
    
    /**
     * 注销工具
     * @param toolName 要注销的工具名称
     * @return 如果成功注销返回true，如果工具不存在返回false
     */
    boolean unregisterTool(String toolName);
    
    /**
     * 清空所有已注册的工具
     */
    void clearAllTools();
    
    /**
     * 工具执行异常
     */
    class ToolExecutionException extends Exception {
        private final String toolName;
        private final String errorCode;
        
        public ToolExecutionException(String toolName, String message) {
            super(message);
            this.toolName = toolName;
            this.errorCode = "EXECUTION_ERROR";
        }
        
        public ToolExecutionException(String toolName, String message, Throwable cause) {
            super(message, cause);
            this.toolName = toolName;
            this.errorCode = "EXECUTION_ERROR";
        }
        
        public ToolExecutionException(String toolName, String errorCode, String message) {
            super(message);
            this.toolName = toolName;
            this.errorCode = errorCode;
        }
        
        public ToolExecutionException(String toolName, String errorCode, String message, Throwable cause) {
            super(message, cause);
            this.toolName = toolName;
            this.errorCode = errorCode;
        }
        
        public String getToolName() { return toolName; }
        public String getErrorCode() { return errorCode; }
    }
}
