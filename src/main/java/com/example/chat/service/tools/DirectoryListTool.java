package com.example.chat.service.tools;

import com.example.chat.service.functioncall.Tool;
import com.example.chat.service.functioncall.ToolExecutionResult;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 目录列表工具 - 类似于 ls 命令
 */
public class DirectoryListTool {
    
    /**
     * 创建目录列表工具
     * @return Tool对象
     */
    public static Tool createTool() {
        List<Tool.ToolParameter> parameters = Arrays.asList(
            new Tool.ToolParameter("path", "string", "要列出的目录路径，默认为当前目录", false, "."),
            new Tool.ToolParameter("show_hidden", "boolean", "是否显示隐藏文件", false, false),
            new Tool.ToolParameter("detailed", "boolean", "是否显示详细信息（文件大小、修改时间等）", false, false),
            new Tool.ToolParameter("sort_by", "string", "排序方式", false, "name", 
                Arrays.asList("name", "size", "modified", "type"))
        );
        
        return new Tool("list_directory", "列出指定目录的文件和子目录", parameters, DirectoryListTool::execute);
    }
    
    /**
     * 执行目录列表功能
     */
    private static ToolExecutionResult execute(Map<String, Object> arguments) throws Exception {
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取参数
            String pathStr = (String) arguments.getOrDefault("path", ".");
            boolean showHidden = (Boolean) arguments.getOrDefault("show_hidden", false);
            boolean detailed = (Boolean) arguments.getOrDefault("detailed", false);
            String sortBy = (String) arguments.getOrDefault("sort_by", "name");
            
            // 验证路径
            Path path = Paths.get(pathStr);
            if (!Files.exists(path)) {
                return ToolExecutionResult.failure("PATH_NOT_FOUND", "路径不存在: " + pathStr);
            }
            
            if (!Files.isDirectory(path)) {
                return ToolExecutionResult.failure("NOT_DIRECTORY", "指定路径不是目录: " + pathStr);
            }
            
            // 获取目录内容
            File directory = path.toFile();
            File[] files = directory.listFiles();
            
            if (files == null) {
                return ToolExecutionResult.failure("ACCESS_DENIED", "无法访问目录: " + pathStr);
            }
            
            // 过滤隐藏文件
            List<File> fileList = new ArrayList<>();
            for (File file : files) {
                if (showHidden || !file.isHidden()) {
                    fileList.add(file);
                }
            }
            
            // 排序
            sortFiles(fileList, sortBy);
            
            // 生成结果
            StringBuilder result = new StringBuilder();
            result.append("目录: ").append(path.toAbsolutePath()).append("\n");
            result.append("共 ").append(fileList.size()).append(" 项\n\n");
            
            if (detailed) {
                result.append(generateDetailedListing(fileList));
            } else {
                result.append(generateSimpleListing(fileList));
            }
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 创建元数据
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("directory_path", path.toAbsolutePath().toString());
            metadata.put("total_items", fileList.size());
            metadata.put("show_hidden", showHidden);
            metadata.put("detailed", detailed);
            metadata.put("sort_by", sortBy);
            
            return new ToolExecutionResult(result.toString(), executionTime, metadata);
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            return ToolExecutionResult.failure("EXECUTION_ERROR", 
                "执行目录列表时发生错误: " + e.getMessage(), executionTime);
        }
    }
    
    /**
     * 排序文件列表
     */
    private static void sortFiles(List<File> files, String sortBy) {
        switch (sortBy.toLowerCase()) {
            case "size":
                files.sort(Comparator.comparingLong(File::length).reversed());
                break;
            case "modified":
                files.sort(Comparator.comparingLong(File::lastModified).reversed());
                break;
            case "type":
                files.sort((f1, f2) -> {
                    // 目录优先，然后按扩展名排序
                    if (f1.isDirectory() && !f2.isDirectory()) return -1;
                    if (!f1.isDirectory() && f2.isDirectory()) return 1;
                    
                    String ext1 = getFileExtension(f1.getName());
                    String ext2 = getFileExtension(f2.getName());
                    int extCompare = ext1.compareToIgnoreCase(ext2);
                    
                    return extCompare != 0 ? extCompare : f1.getName().compareToIgnoreCase(f2.getName());
                });
                break;
            case "name":
            default:
                files.sort((f1, f2) -> {
                    // 目录优先，然后按名称排序
                    if (f1.isDirectory() && !f2.isDirectory()) return -1;
                    if (!f1.isDirectory() && f2.isDirectory()) return 1;
                    return f1.getName().compareToIgnoreCase(f2.getName());
                });
                break;
        }
    }
    
    /**
     * 生成简单列表
     */
    private static String generateSimpleListing(List<File> files) {
        StringBuilder sb = new StringBuilder();
        
        for (File file : files) {
            if (file.isDirectory()) {
                sb.append("📁 ").append(file.getName()).append("/\n");
            } else {
                sb.append("📄 ").append(file.getName()).append("\n");
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 生成详细列表
     */
    private static String generateDetailedListing(List<File> files) {
        StringBuilder sb = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        // 表头
        sb.append(String.format("%-4s %-20s %-12s %-20s %s\n", 
            "类型", "修改时间", "大小", "权限", "名称"));
        sb.append("─".repeat(80)).append("\n");
        
        for (File file : files) {
            String type = file.isDirectory() ? "📁" : "📄";
            String modifiedTime = dateFormat.format(new Date(file.lastModified()));
            String size = file.isDirectory() ? "<DIR>" : formatFileSize(file.length());
            String permissions = getPermissions(file);
            String name = file.getName() + (file.isDirectory() ? "/" : "");
            
            sb.append(String.format("%-4s %-20s %-12s %-20s %s\n", 
                type, modifiedTime, size, permissions, name));
        }
        
        return sb.toString();
    }
    
    /**
     * 格式化文件大小
     */
    private static String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024));
        return String.format("%.1f GB", size / (1024.0 * 1024 * 1024));
    }
    
    /**
     * 获取文件权限字符串
     */
    private static String getPermissions(File file) {
        StringBuilder sb = new StringBuilder();
        sb.append(file.canRead() ? "r" : "-");
        sb.append(file.canWrite() ? "w" : "-");
        sb.append(file.canExecute() ? "x" : "-");
        return sb.toString();
    }
    
    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(lastDot + 1) : "";
    }
}
