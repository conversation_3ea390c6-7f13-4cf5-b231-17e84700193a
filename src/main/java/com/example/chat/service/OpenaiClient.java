package com.example.chat.service;

import com.example.chat.config.OpenAIConfig;
import com.example.chat.model.OpenAIRequest;
import com.example.chat.model.OpenAIResponse;
import com.example.chat.service.functioncall.FunctionCallManager;
import com.example.chat.service.functioncall.ToolExecutionResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * OpenAI API 客户端
 */
public class OpenaiClient {

    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private FunctionCallManager functionCallManager;

    public OpenaiClient() {
        // 配置 HTTP 客户端
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(OpenAIConfig.DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .readTimeout(OpenAIConfig.DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .writeTimeout(OpenAIConfig.DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .build();

        this.objectMapper = new ObjectMapper();
    }

    /**
     * 设置Function Call管理器
     */
    public void setFunctionCallManager(FunctionCallManager functionCallManager) {
        this.functionCallManager = functionCallManager;
    }

    /**
     * 生成 OpenAI 响应 - 返回完整的消息列表
     */
    public List<OpenAIRequest.Message> generateOpenaiResponse(List<OpenAIRequest.Message> messageHistory, String userMessage) {
        try {
            // 检查 API Key
            if (!OpenAIConfig.isApiKeyValid()) {
                List<OpenAIRequest.Message> errorResult = new ArrayList<>();
                if (messageHistory != null) {
                    errorResult.addAll(messageHistory);
                }
                if (userMessage != null && !userMessage.trim().isEmpty()) {
                    errorResult.add(new OpenAIRequest.Message("user", userMessage));
                }
                errorResult.add(new OpenAIRequest.Message("assistant",
                    "错误：OpenAI API Key 未配置或无效。请设置环境变量 OPENAI_API_KEY。"));
                return errorResult;
            }

            // 构建完整的消息历史（包括新的用户消息）
            List<OpenAIRequest.Message> fullHistory = new ArrayList<>();
            if (messageHistory != null) {
                fullHistory.addAll(messageHistory);
            }
            if (userMessage != null && !userMessage.trim().isEmpty()) {
                fullHistory.add(new OpenAIRequest.Message("user", userMessage));
            }

            // 构建请求消息列表
            List<OpenAIRequest.Message> requestMessages = buildRequestMessages(fullHistory, null);

            // 创建请求对象
            OpenAIRequest request = new OpenAIRequest(OpenAIConfig.DEFAULT_MODEL, requestMessages);
            request.setTemperature(OpenAIConfig.DEFAULT_TEMPERATURE);
            request.setMaxTokens(OpenAIConfig.DEFAULT_MAX_TOKENS);

            // 如果有Function Call管理器，添加工具定义
            if (functionCallManager != null && !functionCallManager.getRegisteredTools().isEmpty()) {
                List<Map<String, Object>> toolDefinitions = functionCallManager.getOpenAIFunctionDefinitions();
                request.setTools(new ArrayList<>(toolDefinitions));
                request.setToolChoice("auto"); // 让AI自动决定是否调用工具

                // 发送请求并获取响应，可能需要处理工具调用
                return handleRequestWithPossibleToolCalls(request);
            } else {
                // 没有Function Call管理器，使用原来的逻辑
                OpenAIResponse response = sendRequest(request);
                return processResponseToMessages(response, fullHistory);
            }

        } catch (Exception e) {
            System.err.println("调用 OpenAI API 时发生错误: " + e.getMessage());
            e.printStackTrace();

            // 返回包含错误信息的消息列表
            List<OpenAIRequest.Message> errorResult = new ArrayList<>();
            if (messageHistory != null) {
                errorResult.addAll(messageHistory);
            }
            if (userMessage != null && !userMessage.trim().isEmpty()) {
                errorResult.add(new OpenAIRequest.Message("user", userMessage));
            }
            errorResult.add(new OpenAIRequest.Message("assistant",
                "抱歉，我现在无法回复。请稍后再试。错误信息：" + e.getMessage()));
            return errorResult;
        }
    }

    /**
     * 兼容性方法 - 返回字符串响应（向后兼容）
     */
    public String generateOpenaiResponseString(List<OpenAIRequest.Message> messageHistory, String userMessage) {
        List<OpenAIRequest.Message> messages = generateOpenaiResponse(messageHistory, userMessage);

        // 返回最后一条 assistant 消息的内容
        for (int i = messages.size() - 1; i >= 0; i--) {
            OpenAIRequest.Message msg = messages.get(i);
            if ("assistant".equals(msg.getRole()) && msg.getContent() != null) {
                return msg.getContent();
            }
        }

        return "未收到有效响应";
    }

    /**
     * 构建请求消息列表
     */
    private List<OpenAIRequest.Message> buildRequestMessages(List<OpenAIRequest.Message> messageHistory, String userMessage) {
        // 添加新的用户消息（如果提供）
        if (userMessage != null && !userMessage.trim().isEmpty()) {
            messageHistory.add(new OpenAIRequest.Message("user", userMessage));
        }

        return messageHistory;
    }

    /**
     * 判断是否为基本消息（适合发送给 OpenAI API 的消息）
     */
    private boolean isBasicMessage(OpenAIRequest.Message message) {
        if (message.getRole() == null) {
            return false;
        }

        String role = message.getRole();

        // 系统消息、用户消息、助手消息（无工具调用）
        if ("system".equals(role) || "user".equals(role)) {
            return message.getContent() != null && !message.getContent().trim().isEmpty();
        }

        // 助手消息：要么有内容，要么有工具调用，但不能都没有
        if ("assistant".equals(role)) {
            boolean hasContent = message.getContent() != null && !message.getContent().trim().isEmpty();
            boolean hasToolCalls = message.getToolCalls() != null && !message.getToolCalls().isEmpty();
            return hasContent || hasToolCalls;
        }

        // 跳过工具消息和函数消息，因为它们需要特殊的上下文
        if ("tool".equals(role) || "function".equals(role)) {
            return false;
        }

        return false;
    }

    /**
     * 发送请求到 OpenAI API
     */
    private OpenAIResponse sendRequest(OpenAIRequest request) throws IOException {
        // 序列化请求对象
        String requestJson = objectMapper.writeValueAsString(request);

        // 创建请求体
        RequestBody requestBody = RequestBody.create(
            requestJson,
            MediaType.parse("application/json; charset=utf-8")
        );

        // 构建 HTTP 请求
        Request httpRequest = new Request.Builder()
                .url(OpenAIConfig.getChatCompletionsUrl())
                .addHeader("Authorization", "Bearer " + OpenAIConfig.getApiKey())
                .addHeader("Content-Type", "application/json")
                .addHeader("User-Agent", "ChatApp/1.0")
                .post(requestBody)
                .build();

        // 发送请求
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body().string();

            if (!response.isSuccessful()) {
                throw new IOException("HTTP " + response.code() + ": " + responseBody);
            }

            // 解析成功响应
            return objectMapper.readValue(responseBody, OpenAIResponse.class);
        }
    }

    /**
     * 处理 OpenAI API 响应并返回完整的消息列表
     */
    private List<OpenAIRequest.Message> processResponseToMessages(OpenAIResponse response, List<OpenAIRequest.Message> fullHistory) {

        if (response == null) {
            fullHistory.add(new OpenAIRequest.Message("assistant", "错误：收到空响应"));
            return fullHistory;
        }

        if (response.getError() != null) {
            fullHistory.add(new OpenAIRequest.Message("assistant", "OpenAI API 错误：" + response.getError().getMessage()));
            return fullHistory;
        }

        if (response.getChoices() == null || response.getChoices().isEmpty()) {
            fullHistory.add(new OpenAIRequest.Message("assistant", "错误：响应中没有选择项"));
            return fullHistory;
        }

        // 处理所有选择项（通常只有一个）
        for (OpenAIResponse.Choice choice : response.getChoices()) {
            if (choice.getMessage() != null) {
                String role = choice.getMessage().getRole();
                String content = choice.getMessage().getContent();

                if (role != null && content != null) {
                    OpenAIRequest.Message assistantMessage = new OpenAIRequest.Message(role, content.trim());

                    // 设置完成原因（如果需要的话）
                    if (choice.getFinishReason() != null) {
                        // 可以在这里添加完成原因的处理逻辑
                        System.out.println("完成原因: " + choice.getFinishReason());
                    }

                    fullHistory.add(assistantMessage);
                }
            }
        }

        // 打印使用统计（如果有）
        if (response.getUsage() != null) {
            System.out.println("Token 使用统计: " +
                "提示=" + response.getUsage().getPromptTokens() +
                ", 完成=" + response.getUsage().getCompletionTokens() +
                ", 总计=" + response.getUsage().getTotalTokens());
        }

        return fullHistory;
    }

    /**
     * 处理 OpenAI API 响应（向后兼容的字符串版本）
     */
    private String processResponse(OpenAIResponse response) {
        if (response == null) {
            return "错误：收到空响应";
        }

        if (response.getError() != null) {
            return "OpenAI API 错误：" + response.getError().getMessage();
        }

        if (response.getChoices() == null || response.getChoices().isEmpty()) {
            return "错误：响应中没有选择项";
        }

        OpenAIResponse.Choice firstChoice = response.getChoices().get(0);
        if (firstChoice.getMessage() == null || firstChoice.getMessage().getContent() == null) {
            return "错误：响应消息为空";
        }

        String content = firstChoice.getMessage().getContent().trim();

        // 打印使用统计（如果有）
        if (response.getUsage() != null) {
            System.out.println("Token 使用统计: " +
                "提示=" + response.getUsage().getPromptTokens() +
                ", 完成=" + response.getUsage().getCompletionTokens() +
                ", 总计=" + response.getUsage().getTotalTokens());
        }

        return content;
    }

    /**
     * 设置 API Key（运行时设置）
     */
    public void setApiKey(String apiKey) {
        OpenAIConfig.setApiKey(apiKey);
    }

    /**
     * 检查 API Key 是否有效
     */
    public boolean isApiKeyValid() {
        return OpenAIConfig.isApiKeyValid();
    }

    /**
     * 处理可能包含工具调用的请求
     */
    private List<OpenAIRequest.Message> handleRequestWithPossibleToolCalls(OpenAIRequest request) throws IOException {
        int maxIterations = 5; // 防止无限循环
        int iteration = 0;

        while (iteration < maxIterations) {
            iteration++;

            // 发送请求
            OpenAIResponse response = sendRequest(request);

            // 处理响应
            if (response == null || response.getChoices() == null || response.getChoices().isEmpty()) {
                request.getMessages().add(new OpenAIRequest.Message("assistant", "错误：收到空响应"));
                break;
            }

            OpenAIResponse.Choice choice = response.getChoices().get(0);
            if (choice.getMessage() == null) {
                request.getMessages().add(new OpenAIRequest.Message("assistant", "错误：响应消息为空"));
                break;
            }

            OpenAIResponse.Message responseMessage = choice.getMessage();

            // 创建助手消息
            OpenAIRequest.Message assistantMessage = new OpenAIRequest.Message("assistant", responseMessage.getContent());

            // 检查是否有工具调用
            if (responseMessage.getToolCalls() != null && !responseMessage.getToolCalls().isEmpty()) {
                // 转换工具调用格式
                List<OpenAIRequest.ToolCall> toolCalls = convertToolCalls(responseMessage.getToolCalls());
                assistantMessage.setToolCalls(toolCalls);
                request.getMessages().add(assistantMessage);

                // 执行工具调用
                boolean hasValidToolCalls = false;
                for (OpenAIRequest.ToolCall toolCall : toolCalls) {
                    if (functionCallManager != null && toolCall.getFunction() != null) {
                        try {
                            String toolName = toolCall.getFunction().getName();
                            String arguments = toolCall.getFunction().getArguments();

                            ToolExecutionResult result = functionCallManager.callTool(toolName, arguments);

                            // 创建工具结果消息
                            OpenAIRequest.Message toolMessage = new OpenAIRequest.Message("tool", result.getFormattedResult());
                            toolMessage.setToolCallId(toolCall.getId());
                            toolMessage.setName(toolName);
                            request.getMessages().add(toolMessage);

                            hasValidToolCalls = true;

                        } catch (FunctionCallManager.ToolExecutionException e) {
                            // 工具执行失败，添加错误消息
                            OpenAIRequest.Message errorMessage = new OpenAIRequest.Message("tool",
                                "工具执行失败: " + e.getMessage());
                            errorMessage.setToolCallId(toolCall.getId());
                            errorMessage.setName(toolCall.getFunction().getName());
                            request.getMessages().add(errorMessage);
                        }
                    }
                }

                if (hasValidToolCalls) {
                    // 更新请求消息，继续对话
//                    request.setMessages(buildRequestMessages(fullHistory, null));
                    continue;
                }
            } else {
                // 没有工具调用，正常结束
                if (responseMessage.getContent() != null && !responseMessage.getContent().trim().isEmpty()) {
                    request.getMessages().add(assistantMessage);
                }
                break;
            }
        }

        return request.getMessages();
    }

    /**
     * 转换工具调用格式
     */
    private List<OpenAIRequest.ToolCall> convertToolCalls(List<Object> toolCalls) {
        List<OpenAIRequest.ToolCall> result = new ArrayList<>();

        for (Object toolCallObj : toolCalls) {
            if (toolCallObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> toolCallMap = (Map<String, Object>) toolCallObj;

                String id = (String) toolCallMap.get("id");
                String type = (String) toolCallMap.get("type");

                @SuppressWarnings("unchecked")
                Map<String, Object> functionMap = (Map<String, Object>) toolCallMap.get("function");

                if (functionMap != null) {
                    String name = (String) functionMap.get("name");
                    String arguments = (String) functionMap.get("arguments");

                    OpenAIRequest.FunctionCall function = new OpenAIRequest.FunctionCall(name, arguments);
                    OpenAIRequest.ToolCall toolCall = new OpenAIRequest.ToolCall(id, type, function);
                    result.add(toolCall);
                }
            }
        }

        return result;
    }
}
