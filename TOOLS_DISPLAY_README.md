# 工具展示功能说明

## 概述

工具展示功能允许CopilotChatAssistant向用户展示所有可用的工具，用户可以通过直观的界面了解每个工具的功能、参数和使用方法。

## 功能特性

### 1. 工具面板
- **位置**: 聊天界面左侧（可切换显示/隐藏）
- **触发**: 点击标题栏的工具按钮（🔧）
- **内容**: 显示所有可用工具的卡片列表

### 2. 工具卡片
每个工具卡片包含：
- **工具名称**: 粗体显示，便于识别
- **工具描述**: 详细说明工具的功能
- **参数数量**: 显示工具需要的参数个数
- **交互效果**: 鼠标悬停高亮，点击查看详情

### 3. 工具详情
点击工具卡片后显示：
- 工具完整名称和描述
- 所有参数的详细信息：
  - 参数名称和类型
  - 是否必需
  - 参数描述

## API接口

### ChatInterface新增方法

```java
/**
 * 设置可用工具列表
 * @param tools 可用的工具列表
 */
void setAvailableTools(List<Tool> tools);
```

## 使用方法

### 1. 基本使用

```java
// 创建聊天助手
CopilotChatAssistant chatAssistant = new CopilotChatAssistant();

// 创建工具列表
List<Tool> tools = new ArrayList<>();
tools.add(DirectoryListTool.createTool());
tools.add(SystemInfoTool.createTool());

// 设置工具列表
chatAssistant.setAvailableTools(tools);

// 显示界面
chatAssistant.setVisible(true);
```

### 2. 与FunctionCallManager集成

```java
// 创建功能管理器
FunctionCallManager functionManager = new DefaultFunctionCallManager();
functionManager.registerTool(DirectoryListTool.createTool());
functionManager.registerTool(SystemInfoTool.createTool());

// 获取已注册工具并设置到聊天助手
List<Tool> registeredTools = functionManager.getRegisteredTools();
chatAssistant.setAvailableTools(registeredTools);
```

### 3. 动态更新工具列表

```java
// 添加新工具
functionManager.registerTool(newTool);

// 更新界面显示
chatAssistant.setAvailableTools(functionManager.getRegisteredTools());
```

## 界面设计

### 1. 布局结构
```
┌─────────────────────────────────────────┐
│ GitHub Copilot              🔧 🗑 ⚙    │ ← 标题栏
├─────────────┬───────────────────────────┤
│ 工具面板    │ 聊天区域                  │
│             │                           │
│ ┌─────────┐ │ ┌─────────────────────┐   │
│ │工具卡片1│ │ │ 消息气泡            │   │
│ └─────────┘ │ └─────────────────────┘   │
│             │                           │
│ ┌─────────┐ │ ┌─────────────────────┐   │
│ │工具卡片2│ │ │ 消息气泡            │   │
│ └─────────┘ │ └─────────────────────┘   │
│             │                           │
└─────────────┴───────────────────────────┤
│ 输入框                          [发送]  │ ← 输入区域
└─────────────────────────────────────────┘
```

### 2. 视觉设计
- **工具面板**: 浅灰色背景（#F8F9FA），宽度300px
- **工具卡片**: 白色背景，圆角8px，悬停时变为浅灰色
- **字体**: 工具名称使用粗体，描述使用常规字体
- **颜色**: 主文本深色，辅助文本灰色

### 3. 交互设计
- **显示/隐藏**: 点击工具按钮切换面板显示状态
- **悬停效果**: 鼠标悬停时工具卡片背景变色
- **点击查看**: 点击工具卡片弹出详情对话框
- **响应式**: 工具面板可以根据需要显示或隐藏

## 测试示例

### 1. 基础测试
运行 `ToolsDisplayTest.java` 查看基本的工具展示功能。

### 2. 集成测试
运行 `IntegratedToolsTest.java` 查看完整的集成效果，包括：
- 工具展示
- 消息处理
- AI回复中的工具介绍

## 扩展功能

### 1. 工具分类
可以扩展为按类别分组显示工具：
```java
// 未来可能的扩展
chatAssistant.setAvailableToolsByCategory(Map<String, List<Tool>> toolsByCategory);
```

### 2. 工具搜索
可以添加搜索功能：
```java
// 未来可能的扩展
toolsPanel.addSearchBox();
```

### 3. 工具使用统计
可以显示工具使用频率：
```java
// 未来可能的扩展
tool.setUsageCount(count);
```

## 注意事项

1. **性能**: 工具列表较大时，考虑使用虚拟滚动
2. **内存**: 及时清理不再使用的工具引用
3. **线程安全**: 工具列表更新需要在EDT线程中进行
4. **用户体验**: 工具面板不应影响正常的聊天功能

## 总结

工具展示功能为用户提供了直观的方式来了解和使用AI助手的各种工具能力，提升了用户体验和工具的可发现性。通过简洁的API设计，开发者可以轻松地集成和定制工具展示功能。
